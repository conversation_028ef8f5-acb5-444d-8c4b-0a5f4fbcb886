<template>
    <div>
        <!-- 在template修改head的方法 -->

        <Head>
            <Title>Nuxt Dojo | {{ product.title }}</Title>
            <Meta name="description" :content="product.description" />
        </Head>


        <div class="container">
            <ProductDetails :product="product" :CNYPrice="CNYPrice" />
        </div>
    </div>
</template>

<script setup>
import ProductDetails from '~/components/ProductDetails.vue';

definePageMeta({
    title: 'Product Details',
    layout: 'products'
})

const { id } = useRoute().params

const uri = `https://fakestoreapi.com/products/${id}`
const { data: product } = await useFetch(uri, { key: id })

if (!product.value) {
    throw createError({ statusCode: 404, statusMessage: 'Product not found', fatal: true })
}

const { data } = await useFetch(`/api/currency/CNY`)

console.log('Running on:', process.client ? 'Client' : 'Server');
console.log('Rate is :', data.value.CNY.value);
const rate = data.value.CNY.value
const CNYPrice = computed(() => { return (product.value.price * rate).toFixed(2) }
)
console.log('CNYPrice is :', CNYPrice.value);
</script>

<style scoped></style>

<template>
    <div>
        <NuxtLink :to="`/products/${product.id}`" class="font-bold mb-2 block">{{ product.title }}</NuxtLink>
        <p class="text-sm text-gray-600 mb-2">{{ product.category }}</p>
        <p class="text-lg font-semibold mb-2">${{ product.price }}</p>
        <NuxtLink :to="`/products/${product.id}`"><img :src="product.image" alt="Product Image"
                class="w-full h-48 object-contain mb-2" /></NuxtLink>
    </div>
</template>

<script setup>
const props = defineProps({
    product: {
        type: Object,
        required: true
    }
})
</script>

<style scoped></style>
<template>
    <div class="card">
        <div class="grid grid-cols-2 gap-10 max-lg:grid-cols-1">
            <div class="p-7">
                <img :src="`${product.image}`" class="mx-auto my-7 max-lg:w-[95%]" />
            </div>
            <div class="p-7">
                <h2 class="text-4xl my-7">{{ product.title }}</h2>
                <p class="text-xl my-7">${{ product.price }}</p>
                <p class="text-xl my-7 text-green-500">¥{{ CNYPrice }}</p>
                <h3 class="font-bold border-b-2 mb-4 pb-2">Product description:</h3>
                <p class="mb-7">{{ product.description }}</p>
                <button class="btn flex">
                    <i class="material-icons mr-2">add_shopping_cart</i>
                    <span>Add to cart</span>
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
const { product, CNYPrice } = defineProps(['product', 'CNYPrice'])
</script>

<style scoped>
img {
    max-width: 400px;
}
</style>
<template>
    <div>
        <div class="grid lg:grid-cols-4 gap-5">
            <div v-for="product in products" :key="product.id" class="border p-4 rounded-lg">
                <ProductCard :product="product" />
            </div>

        </div>
    </div>
</template>

<script setup lang="ts">
definePageMeta({
    title: 'Products Page',
    layout: 'products'
})



const { data: products } = await useFetch<Product[]>('https://fakestoreapi.com/products')

onMounted(() => {
    console.log('Products page mounted');
    console.log(products.value);
})

// 在script覆盖metadata的方法
useHead({
    title: 'Nuxt Dojo | Merch',
    meta: [
        { name: 'description', content: 'This is the products page' }
    ]
})
</script>

<style scoped></style>
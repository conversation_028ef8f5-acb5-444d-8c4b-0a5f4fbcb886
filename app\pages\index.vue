<template>
    <section class="bg-white min-h-screen flex flex-col justify-center items-center px-4 py-16">
        <div class="max-w-6xl w-full flex flex-col md:flex-row items-center gap-12">
            <!-- 左侧文字区 -->
            <div class="flex-1">
                <span class="text-green-600 font-semibold text-lg mb-4 block">New release</span>
                <h1 class="text-5xl md:text-6xl font-extrabold text-gray-900 leading-tight mb-6">
                    Ultimate<br>Clothing<br>Mall
                </h1>
                <p class="text-gray-500 text-xl mb-8">
                    Offering a wide range of
                    high-quality products including apparel, accessories, and limited-edition collectibles. Show your
                    love for <PERSON><PERSON><PERSON> and experience the ultimate developer culture.
                </p>
                <div class="flex gap-4">
                    <NuxtLink to="/products">
                        <button
                            class="bg-green-500 hover:bg-green-600 text-white font-semibold px-6 py-3 rounded-lg flex items-center gap-2 shadow">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 12h14M12 5l7 7-7 7" />
                            </svg>
                            Get started
                        </button>
                    </NuxtLink>
                    <NuxtLink to="/products">
                        <button
                            class="bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold px-6 py-3 rounded-lg flex items-center gap-2 border border-gray-300">
                            Learn more
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17 8l4 4m0 0l-4 4m4-4H3" />
                            </svg>
                        </button>
                    </NuxtLink>
                </div>
            </div>
            <!-- 右侧配图区 -->
            <div class="flex-1 flex justify-center items-center">
                <div class="rounded-2xl shadow-xl overflow-hidden bg-white">
                    <img src="~/assets/img/heroImg.png" alt="UI Library Preview" class="w-[480px] h-auto object-cover">
                </div>
            </div>
        </div>
    </section>
</template>


<style scoped></style>